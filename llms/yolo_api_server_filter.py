# 使用yolo进行目标检测
from ultralytics import YOLO
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import cv2
import os
import sys
import time
import logging

# from llms.utils.logger import setup_logging
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# 移除图像处理导入
# from llms.utils.transform_processor import process_image
from server.utils.logger import setup_logging
# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)

class DeviceDetector:
    def __init__(self, model_path: str = "llms/models/yolo/best-filter.pt"):
        """初始化设备检测器
        
        Args:
            model_path: YOLO模型路径
        """
        self.model = YOLO(model_path, task="detect")
        logger.info(f"滤池检测器初始化完成，使用模型: {model_path}")
        
    def detect_devices(self, image: Union[np.ndarray, str], save_result: bool = False, output_path: str = None) -> Dict[str, List[Dict]]:
        """检测图像中的设备状态

        Args:
            image: 输入图像数组(np.ndarray)或图片路径(str)
            save_result: 是否保存识别结果图像
            output_path: 指定保存路径，如果为None则保存到tests文件夹

        Returns:
            Dict包含:
            - devices: 检测到的设备列表
              每个设备包含:
              - id: 设备序号(从0开始)
              - status: 状态("malfunction"或其他正常状态)
              - confidence: 检测置信度
              - bbox: 边界框坐标[x1,y1,x2,y2]
            - annotated_image: 标注后的图像数组(np.ndarray)
            - image_path: 如果save_result=True，则包含保存的结果图像路径
        """
        # 如果输入是图片路径，则使用cv2读取图片
        input_path = None
        if isinstance(image, str):
            if not os.path.exists(image):
                raise FileNotFoundError(f"图片路径不存在: {image}")
            input_path = image
            image = cv2.imread(image)
            if image is None:
                raise ValueError(f"无法读取图片: {input_path}")
        
        # 确保image是np.ndarray类型
        if not isinstance(image, np.ndarray):
            raise TypeError(f"image必须是np.ndarray或str类型，当前类型: {type(image)}")
            
        # 直接使用原始图像，移除图像处理步骤
        processed_image = image
        
        # 使用YOLO模型进行检测
        results = self.model(processed_image, conf=0.6, iou=0.6, max_det=10, imgsz=640, device='cpu')  # 使用cpu推理
        
        # 解析结果
        devices = []
        if len(results) > 0:
            result = results[0]  # 获取第一张图片的结果
            boxes = result.boxes
            
            # 处理检测框
            if len(boxes) > 0:
                for i in range(len(boxes)):
                    box = boxes[i]
                    bbox = box.xyxy[0].cpu().numpy().tolist()  # 边界框坐标
                    cls_name = result.names[int(box.cls[0])]  # 获取类别名称
                    conf = float(box.conf[0])  # 置信度
                    
                    devices.append({
                        "id": i,
                        "status": cls_name,
                        "confidence": conf,
                        "bbox": bbox
                    })
        
        # 生成标注图像
        annotated_img = results[0].plot()

        # 在图像上标注设备ID和状态信息
        for device in devices:
            bbox = device["bbox"]
            device_id = device["id"]
            status = device["status"]
            confidence = device["confidence"]

            x, y = int(bbox[0]), int(bbox[1]) - 10  # 在边界框左上角上方显示信息

            # 根据状态设置不同颜色
            color = (0, 0, 255) if status == "malfunction" else (0, 255, 0)  # 红色表示故障，绿色表示正常

            # 显示设备ID、状态和置信度
            label = f"ID:{device_id} {status} {confidence:.2f}"
            cv2.putText(annotated_img, label, (x, y),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

        response = {
            "devices": devices,
            "annotated_image": annotated_img
        }

        # 保存结果图像（如果需要）
        if save_result:
            if output_path:
                # 使用指定的输出路径
                save_path = output_path
                # 确保目录存在
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
            else:
                # 使用默认的tests目录
                tests_dir = "tests"
                os.makedirs(tests_dir, exist_ok=True)

                # 生成文件名
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                if input_path:
                    base_name = os.path.basename(input_path)
                    filename = f"{timestamp}_{base_name}"
                else:
                    filename = f"{timestamp}_result.jpg"

                save_path = os.path.join(tests_dir, filename)

            cv2.imwrite(save_path, annotated_img)
            response["image_path"] = save_path
            logger.info(f"滤池检测结果图像已保存: {save_path}")

        return response

# 线程安全修复：移除全局检测器实例
# 根据Ultralytics官方文档建议，避免在多线程环境中共享YOLO模型实例
# 每次使用时应创建独立的检测器实例以确保线程安全
# detector = DeviceDetector()  # 已注释，防止线程安全问题

def get_filter_detector(model_path: str = "llms/models/yolo/best-filter.pt") -> DeviceDetector:
    """
    线程安全的滤池检测器获取函数
    每次调用都返回新的检测器实例，确保线程安全

    Args:
        model_path: YOLO模型路径

    Returns:
        DeviceDetector: 新的滤池检测器实例
    """
    return DeviceDetector(model_path)

# 测试代码
if __name__ == "__main__":
    # 线程安全测试：使用新的检测器获取函数
    test_detector = get_filter_detector()

    # 测试图片路径参数
    results_path = test_detector.detect_devices("assets/5fdcf4d20eaadc93ff7258a968800c0a.png", save_result=True)
    logger.info("路径参数检测结果: %s", results_path)

    # 显式清理资源
    del test_detector
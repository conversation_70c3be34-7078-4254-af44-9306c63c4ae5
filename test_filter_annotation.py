#!/usr/bin/env python3
"""
测试滤池YOLO标注功能
验证方案一的实现效果
"""

import sys
import os
import cv2
import numpy as np
from pathlib import Path

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_filter_detector():
    """测试滤池检测器的标注功能"""
    print("=== 测试滤池YOLO检测器标注功能 ===")
    
    try:
        from llms.yolo_api_server_filter import DeviceDetector
        
        # 创建检测器实例
        detector = DeviceDetector()
        print("✓ 滤池检测器初始化成功")
        
        # 创建一个测试图像（如果没有真实图像的话）
        test_image_path = "tests/test_filter_image.jpg"
        
        # 检查是否有测试图像
        if not os.path.exists(test_image_path):
            print(f"⚠ 测试图像不存在: {test_image_path}")
            print("创建一个模拟图像进行测试...")
            
            # 创建一个模拟图像
            test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            os.makedirs("tests", exist_ok=True)
            cv2.imwrite(test_image_path, test_image)
            print(f"✓ 已创建模拟测试图像: {test_image_path}")
        
        # 测试检测功能
        print(f"开始检测图像: {test_image_path}")
        result = detector.detect_devices(test_image_path, save_result=True)
        
        print("检测结果:")
        print(f"- 检测到设备数量: {len(result.get('devices', []))}")
        
        # 检查是否有标注图像
        if 'annotated_image' in result:
            print("✓ 成功获取标注图像")
            annotated_image = result['annotated_image']
            print(f"- 标注图像尺寸: {annotated_image.shape}")
            
            # 保存标注图像到指定位置
            output_path = "tests/annotated_filter_result.jpg"
            cv2.imwrite(output_path, annotated_image)
            print(f"✓ 标注图像已保存到: {output_path}")
        else:
            print("✗ 未获取到标注图像")
        
        # 检查保存的图像路径
        if 'image_path' in result:
            print(f"✓ 检测结果图像已保存到: {result['image_path']}")
        
        # 显示设备检测详情
        devices = result.get('devices', [])
        if devices:
            print("\n设备检测详情:")
            for device in devices:
                print(f"  设备ID: {device['id']}")
                print(f"  状态: {device['status']}")
                print(f"  置信度: {device['confidence']:.2f}")
                print(f"  边界框: {device['bbox']}")
                print("  ---")
        else:
            print("未检测到任何设备（这在模拟图像中是正常的）")
            
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_filter_handler():
    """测试滤池处理器的新功能"""
    print("\n=== 测试滤池处理器标注功能 ===")
    
    try:
        from server.utils.handlers.filter_handler import FilterHandler
        from server.utils.video_processor import VideoFrameProcessor
        from config_file import config
        
        # 创建视频处理器
        processor = VideoFrameProcessor(config.env)
        print("✓ 视频处理器初始化成功")
        
        # 创建滤池处理器
        handler = FilterHandler(processor)
        print("✓ 滤池处理器初始化成功")
        
        # 检查新方法是否存在
        if hasattr(handler, '_process_image_comparison_with_annotation'):
            print("✓ 新的标注方法已添加")
        else:
            print("✗ 新的标注方法未找到")
            
        if hasattr(handler, '_save_annotated_frame'):
            print("✓ 标注图像保存方法已添加")
        else:
            print("✗ 标注图像保存方法未找到")
            
        if hasattr(processor, '_get_last_annotated_image'):
            print("✓ 获取标注图像方法已添加")
        else:
            print("✗ 获取标注图像方法未找到")
            
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试滤池YOLO标注功能实现...")
    
    # 测试1: 检测器功能
    test1_result = test_filter_detector()
    
    # 测试2: 处理器功能
    test2_result = test_filter_handler()
    
    # 总结
    print("\n=== 测试总结 ===")
    if test1_result and test2_result:
        print("✓ 所有测试通过！方案一实现成功")
        print("\n实现的功能:")
        print("1. YOLO检测器现在返回标注图像")
        print("2. 滤池处理器可以保存标注图像")
        print("3. 视频处理器可以传递标注图像")
        print("\n用户现在将看到带有检测结果标注的图像！")
    else:
        print("✗ 部分测试失败，请检查实现")
        
    return test1_result and test2_result

if __name__ == "__main__":
    main()
